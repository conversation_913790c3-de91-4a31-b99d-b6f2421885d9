<template>
  <div class="mobile-page product-page">
    <!-- 移动端导航头部 -->
    <MobileHeader
      :title="pageTitle"
      :show-back="true"
      :back-url="backUrl"
      :show-logo="false"
      :show-home="true"
    />

    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 主要内容区域 -->
      <section class="main-content">
        <div class="mobile-container">
          <!-- 产品展示卡片 -->
          <div class="product-showcase-card">
            <!-- 产品标题区域 -->
            <div class="product-header">
              <div class="product-title-wrapper">
                <!-- 中文标题 -->
                <h1 class="product-title-cn">{{ productTitleCn }}</h1>
                <!-- 英文标题 -->
                <h2 class="product-title-en">{{ productTitleEn }}</h2>
              </div>
            </div>

            <!-- 产品图片展示区域 -->
            <div class="product-image-section">
              <!-- 主要产品图片 -->
              <div v-if="heroImage" class="hero-image-container">
                <img
                  :src="heroImage.src"
                  :alt="heroImage.alt"
                  class="product-main-image"
                />
              </div>

              <!-- 产品列表 -->
              <div v-if="productList && productList.length" class="product-list">
                <div
                  v-for="(product, index) in productList"
                  :key="index"
                  class="product-item"
                >
                  <!-- 产品图片 -->
                  <div class="product-item-image">
                    <img
                      :src="product.image"
                      :alt="product.titleCn"
                      class="product-image"
                    />
                  </div>

                  <!-- 产品信息 -->
                  <div class="product-item-info">
                    <!-- 主标题（条件渲染） -->
                    <h2
                      v-if="product.mainTitle || product.primaryTitle"
                      class="product-item-main-title"
                    >
                      {{ product.mainTitle || product.primaryTitle }}
                    </h2>

                    <!-- 中文标题 -->
                    <h3 class="product-item-title-cn">{{ product.titleCn }}</h3>

                    <!-- 英文标题 -->
                    <h4 class="product-item-title-en">{{ product.titleEn }}</h4>

                    <!-- 产品特点列表 -->
                    <div v-if="product.features && product.features.length" class="product-item-features">
                      <ul class="features-list">
                        <li
                          v-for="(feature, featureIndex) in product.features"
                          :key="featureIndex"
                          class="feature-item"
                        >
                          <div class="feature-icon">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                          </div>
                          <div class="feature-content">
                            <!-- 支持对象格式（中英文）和字符串格式（向后兼容） -->
                            <template v-if="typeof feature === 'object' && feature.cn">
                              <span class="feature-text-cn">{{ feature.cn }}</span>
                              <span v-if="feature.en" class="feature-text-en">{{ feature.en }}</span>
                            </template>
                            <template v-else>
                              <span class="feature-text">{{ feature }}</span>
                            </template>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 页脚 -->
    <MobileFooter
      :show-back-to-top="true"
      :show-more-content="showMoreContent"
      :more-content-url="moreContentUrl"
      :quick-links="footerLinks"
      :copyright-text="copyrightText"
      :company-info="companyInfo"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePageData, usePageFrontmatter } from 'vuepress/client'
import MobileFooter from '../components/MobileFooter.vue'
import MobileHeader from '../components/MobileHeader.vue'

// 获取页面数据
const page = usePageData()
const frontmatter = usePageFrontmatter()

// Props定义
const props = defineProps({
  // 页面标题
  pageTitle: {
    type: String,
    default: ''
  },
  // 产品中文标题
  productTitleCn: {
    type: String,
    default: ''
  },
  // 产品英文标题
  productTitleEn: {
    type: String,
    default: ''
  },
  // 返回URL
  backUrl: {
    type: String,
    default: '/products/'
  },
  // 主要产品图片
  heroImage: {
    type: Object,
    default: null
    // 格式: { src: 'url', alt: 'description' }
  },
  // 产品列表
  productList: {
    type: Array,
    default: () => []
    // 格式: [{ titleCn: '', titleEn: '', image: '', features: [{ cn: '中文特点', en: 'English Feature' }] 或 ['特点1', '特点2'] }]
  },
  // 是否显示更多内容按钮
  showMoreContent: {
    type: Boolean,
    default: false
  },
  // 更多内容URL
  moreContentUrl: {
    type: String,
    default: '/articles/'
  },
  // 页脚快速链接
  footerLinks: {
    type: Array,
    default: () => [
      { text: '返回首页', url: '/' },
      { text: '产品展示', url: '/products/' },
      { text: '生产车间', url: '/workshop/' },
      { text: '资质证书', url: '/qualification/' }
    ]
  },
  // 版权文本
  copyrightText: {
    type: String,
    default: '© 2022-2023 SPINRED 版权所有'
  },
  // 公司信息
  companyInfo: {
    type: String,
    default: '无锡市双马智能科技有限公司'
  }
})

// 计算属性：获取页面标题
const pageTitle = computed(() => {
  return frontmatter.value.pageTitle || props.pageTitle || page.value.title
})

// 计算属性：获取产品标题
const productTitleCn = computed(() => {
  return frontmatter.value.productTitleCn || props.productTitleCn || frontmatter.value.pageTitle || pageTitle.value
})

const productTitleEn = computed(() => {
  return frontmatter.value.productTitleEn || props.productTitleEn || ''
})

// 计算属性：获取其他 frontmatter 数据
const heroImage = computed(() => frontmatter.value.heroImage)
const productList = computed(() => frontmatter.value.productList || props.productList)
const backUrl = computed(() => frontmatter.value.backUrl || props.backUrl)
const showMoreContent = computed(() => frontmatter.value.showMoreContent || props.showMoreContent)
const moreContentUrl = computed(() => frontmatter.value.moreContentUrl || props.moreContentUrl)
</script>

<style lang="scss" scoped>
.product-page {
  background: var(--brand-gray);
  min-height: 100vh;
}

.main-content {
  // padding: var(--spacing-6) 0;

  .product-showcase-card {
    background: var(--brand-white);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin-bottom: var(--spacing-8);

    // 产品标题区域
    .product-header {
      background: var(--gradient-primary);
      color: var(--brand-white);
      padding: var(--spacing-6) var(--spacing-4);
      text-align: center;

      .product-title-wrapper {
        .product-title-cn {
          font-size: var(--font-size-4xl);
          font-weight: 800;
          margin: 0 0 var(--spacing-3) 0;
          line-height: 1.2;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .product-title-en {
          font-size: var(--font-size-xl);
          font-weight: 500;
          margin: 0;
          opacity: 0.95;
          line-height: 1.3;
        }
      }
    }

    // 产品图片展示区域
    .product-image-section {
      padding: var(--spacing-6);

      // 主要产品图片
      .hero-image-container {
        text-align: center;
        margin-bottom: var(--spacing-8);
        padding: var(--spacing-4);

        .product-main-image {
          width: 95%;
          max-width: 800px;
          height: auto;
          border-radius: var(--radius-xl);
          box-shadow: var(--shadow-xl);
          transition: all 0.4s ease;
          cursor: zoom-in;

          &:hover {
            transform: scale(1.03);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
          }
        }
      }

      // 产品列表
      .product-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-6);

        .product-item {
          display: flex;
          align-items: center;
          background: var(--brand-gray);
          border-radius: var(--radius-lg);
          padding: var(--spacing-4);
          transition: all 0.3s ease;

          &:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
          }

          .product-item-image {
            flex-shrink: 0;
            width: 180px;
            height: 180px;
            margin-right: var(--spacing-6);

            .product-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: var(--radius-lg);
              box-shadow: var(--shadow-lg);
              transition: all 0.3s ease;

              &:hover {
                transform: scale(1.05);
                box-shadow: var(--shadow-xl);
              }
            }
          }

          .product-item-info {
            flex: 1;

            .product-item-main-title {
              font-size: var(--font-size-2xl);
              font-weight: 700;
              margin: 0 0 var(--spacing-3) 0;
              line-height: 1.2;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            .product-item-title-cn {
              font-size: var(--font-size-xl);
              font-weight: 600;
              color: var(--brand-dark-gray);
              margin: 0 0 var(--spacing-2) 0;
              line-height: 1.3;
            }

            .product-item-title-en {
              font-size: var(--font-size-base);
              font-weight: 400;
              color: #666;
              margin: 0 0 var(--spacing-3) 0;
              line-height: 1.4;
            }

            // 产品特点列表
            .product-item-features {
              margin-top: var(--spacing-3);

              .features-list {
                list-style: none;
                padding: 0;
                margin: 0;
                display: flex;
                flex-direction: column;
                gap: var(--spacing-2);

                .feature-item {
                  display: flex;
                  align-items: center;
                  padding: var(--spacing-2) var(--spacing-3);
                  background: rgba(var(--brand-primary-rgb), 0.05);
                  border-radius: var(--radius-md);
                  transition: all 0.2s ease;

                  &:hover {
                    background: rgba(var(--brand-primary-rgb), 0.1);
                    transform: translateX(2px);
                  }

                  .feature-icon {
                    flex-shrink: 0;
                    width: 16px;
                    height: 16px;
                    margin-right: var(--spacing-2);
                    color: var(--brand-primary);

                    svg {
                      width: 100%;
                      height: 100%;
                    }
                  }

                  .feature-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: var(--spacing-1);
                  }

                  .feature-text {
                    font-size: var(--font-size-base);
                    font-weight: 500;
                    color: var(--brand-dark-gray);
                    line-height: 1.4;
                  }

                  .feature-text-cn {
                    font-size: var(--font-size-base);
                    font-weight: 600;
                    color: var(--brand-dark-gray);
                    line-height: 1.4;
                  }

                  .feature-text-en {
                    font-size: var(--font-size-sm);
                    font-weight: 400;
                    color: #666;
                    line-height: 1.3;
                    font-style: italic;
                  }
                }
              }
            }
          }
        }
      }
    }



    // 详细内容区域
    .product-detail-content {
      border-top: 1px solid var(--brand-light-gray);
      padding: var(--spacing-6);

      .content-section {
        line-height: 1.8;

        p {
          margin-bottom: var(--spacing-4);
          color: #555;
          text-align: justify;

          &:last-child {
            margin-bottom: 0;
          }
        }

        h1, h2, h3, h4, h5, h6 {
          color: var(--brand-dark-gray);
          margin-top: var(--spacing-6);
          margin-bottom: var(--spacing-4);

          &:first-child {
            margin-top: 0;
          }
        }

        ul, ol {
          margin-bottom: var(--spacing-4);
          padding-left: var(--spacing-6);

          li {
            margin-bottom: var(--spacing-2);
            color: #555;
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 480px) {
  .main-content {
    // padding: var(--spacing-4) 0;

    .product-showcase-card {
      .product-header {
        padding: var(--spacing-4);

        .product-title-wrapper {
          .product-title-cn {
            font-size: var(--font-size-3xl);
            font-weight: 700;
          }

          .product-title-en {
            font-size: var(--font-size-lg);
            font-weight: 500;
          }
        }
      }

      .product-image-section {
        padding: var(--spacing-4);

        .hero-image-container {
          margin-bottom: var(--spacing-6);
          padding: var(--spacing-2);

          .product-main-image {
            width: 100%;
            border-radius: var(--radius-lg);
          }
        }

        .product-list {
          gap: var(--spacing-4);

          .product-item {
            flex-direction: column;
            text-align: center;

            .product-item-image {
              width: 100%;
              height: auto;
              margin-right: 0;
              margin-bottom: var(--spacing-4);
            }

            .product-item-info {
              .product-item-main-title {
                font-size: var(--font-size-xl);
                font-weight: 700;
                margin: 0 0 var(--spacing-2) 0;
              }

              .product-item-title-cn {
                font-size: var(--font-size-lg);
              }

              .product-item-title-en {
                font-size: var(--font-size-sm);
              }

              // 移动端产品特点样式
              .product-item-features {
                margin-top: var(--spacing-2);

                .features-list {
                  gap: var(--spacing-1);

                  .feature-item {
                    padding: var(--spacing-1) var(--spacing-2);

                    .feature-icon {
                      width: 14px;
                      height: 14px;
                      margin-right: var(--spacing-1);
                    }

                    .feature-text {
                      font-size: var(--font-size-sm);
                      font-weight: 500;
                    }

                    .feature-text-cn {
                      font-size: var(--font-size-sm);
                      font-weight: 600;
                    }

                    .feature-text-en {
                      font-size: var(--font-size-sm);
                      font-weight: 400;
                      color: #666;
                      font-style: italic;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .product-detail-content {
        padding: var(--spacing-4);
      }
    }
  }
}

// 平板设备优化 (768px - 1024px)
@media (min-width: 768px) and (max-width: 1024px) {
  .main-content {
    .product-showcase-card {
      .product-header {
        padding: var(--spacing-6) var(--spacing-5);

        .product-title-wrapper {
          .product-title-cn {
            font-size: var(--font-size-3xl);
            font-weight: 700;
          }

          .product-title-en {
            font-size: var(--font-size-xl);
            font-weight: 500;
          }
        }
      }

      .product-image-section {
        padding: var(--spacing-6);

        .hero-image-container {
          margin-bottom: var(--spacing-6);
          padding: var(--spacing-3);

          .product-main-image {
            width: 75%;
            max-width: 600px;
            border-radius: var(--radius-lg);
          }
        }

        .product-list {
          gap: var(--spacing-5);

          .product-item {
            padding: var(--spacing-5);

            .product-item-image {
              width: 150px;
              height: 150px;
              margin-right: var(--spacing-5);
            }

            .product-item-info {
              .product-item-main-title {
                font-size: var(--font-size-xl);
                font-weight: 700;
                margin: 0 0 var(--spacing-2) 0;
              }

              .product-item-title-cn {
                font-size: var(--font-size-lg);
                font-weight: 600;
              }

              .product-item-title-en {
                font-size: var(--font-size-base);
                font-weight: 400;
              }

              // 平板端产品特点样式
              .product-item-features {
                margin-top: var(--spacing-3);

                .features-list {
                  gap: var(--spacing-2);

                  .feature-item {
                    padding: var(--spacing-2) var(--spacing-3);

                    .feature-icon {
                      width: 15px;
                      height: 15px;
                      margin-right: var(--spacing-2);
                    }

                    .feature-text {
                      font-size: var(--font-size-sm);
                      font-weight: 500;
                    }

                    .feature-text-cn {
                      font-size: var(--font-size-sm);
                      font-weight: 600;
                    }

                    .feature-text-en {
                      font-size: var(--font-size-sm);
                      font-weight: 400;
                      color: #666;
                      font-style: italic;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .product-detail-content {
        padding: var(--spacing-6);
      }
    }
  }
}

// 桌面端优化 (1025px+)
@media (min-width: 1025px) {
  .main-content {
    .product-showcase-card {
      .product-header {
        padding: var(--spacing-8) var(--spacing-6);

        .product-title-wrapper {
          .product-title-cn {
            font-size: var(--font-size-4xl);
          }

          .product-title-en {
            font-size: var(--font-size-2xl);
          }
        }
      }

      .product-image-section {
        padding: var(--spacing-8);

        .hero-image-container {
          .product-main-image {
            width: 85%;
            max-width: 900px;
          }
        }

        .product-list {
          .product-item {
            .product-item-image {
              width: 180px;
              height: 180px;
            }

            .product-item-info {
              .product-item-main-title {
                font-size: var(--font-size-2xl);
                font-weight: 700;
                margin: 0 0 var(--spacing-3) 0;
              }

              // 桌面端产品特点样式
              .product-item-features {
                margin-top: var(--spacing-4);

                .features-list {
                  gap: var(--spacing-3);

                  .feature-item {
                    padding: var(--spacing-3) var(--spacing-4);

                    .feature-icon {
                      width: 18px;
                      height: 18px;
                      margin-right: var(--spacing-3);
                    }

                    .feature-text {
                      font-size: var(--font-size-sm);
                      font-weight: 500;
                    }

                    .feature-text-cn {
                      font-size: var(--font-size-sm);
                      font-weight: 600;
                    }

                    .feature-text-en {
                      font-size: var(--font-size-sm);
                      font-weight: 400;
                      color: #666;
                      font-style: italic;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}



// 进入动画
.main-content {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
