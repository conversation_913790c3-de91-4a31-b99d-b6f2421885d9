import comp from "/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/pages/test-product-features.html.vue"
const data = JSON.parse("{\"path\":\"/test-product-features.html\",\"title\":\"产品特点中英文测试页面\",\"lang\":\"zh-CN\",\"frontmatter\":{\"layout\":\"ProductPageLayout\",\"pageTitle\":\"产品特点中英文测试页面\",\"productTitleCn\":\"产品特点中英文测试\",\"productTitleEn\":\"Product Features Bilingual Test\",\"productList\":[{\"titleCn\":\"中英文特点产品\",\"titleEn\":\"Bilingual Features Product\",\"image\":\"https://img-public.hui1688.cn/shuangma/66040eece3f7a.png\",\"features\":[{\"cn\":\"高清TFT显示屏\",\"en\":\"High-definition TFT display\"},{\"cn\":\"蓝牙4.0无线连接\",\"en\":\"Bluetooth 4.0 wireless connection\"},{\"cn\":\"IP67防水防尘\",\"en\":\"IP67 waterproof and dustproof\"}]},{\"primaryTitle\":\"混合格式测试\",\"titleCn\":\"新旧格式兼容测试\",\"titleEn\":\"New and Old Format Compatibility Test\",\"image\":\"https://img-public.hui1688.cn/shuangma/66040eb46ddcb.png\",\"features\":[{\"cn\":\"智能控制系统\",\"en\":\"Smart control system\"},\"旧格式特点（仅中文）\",{\"cn\":\"NFC近场通信\",\"en\":\"NFC near field communication\"},\"超长续航设计\",{\"cn\":\"一键启动功能\",\"en\":\"One-key start function\"}]},{\"titleCn\":\"仅中文特点产品\",\"titleEn\":\"Chinese Only Features Product\",\"image\":\"https://img-public.hui1688.cn/shuangma/product-lcd.jpg\",\"features\":[\"传统字符串格式特点1\",\"传统字符串格式特点2\",\"传统字符串格式特点3\"]},{\"titleCn\":\"无特点产品\",\"titleEn\":\"Product without Features\",\"image\":\"https://img-public.hui1688.cn/shuangma/66040eb46ddcb.png\"}],\"backUrl\":\"/products/\",\"showDetailContent\":false},\"headers\":[{\"level\":2,\"title\":\"测试内容\",\"slug\":\"测试内容\",\"link\":\"#测试内容\",\"children\":[]},{\"level\":2,\"title\":\"预期效果\",\"slug\":\"预期效果\",\"link\":\"#预期效果\",\"children\":[]},{\"level\":2,\"title\":\"数据格式说明\",\"slug\":\"数据格式说明\",\"link\":\"#数据格式说明\",\"children\":[{\"level\":3,\"title\":\"新格式（推荐）\",\"slug\":\"新格式-推荐\",\"link\":\"#新格式-推荐\",\"children\":[]},{\"level\":3,\"title\":\"旧格式（向后兼容）\",\"slug\":\"旧格式-向后兼容\",\"link\":\"#旧格式-向后兼容\",\"children\":[]}]}],\"git\":{},\"filePathRelative\":\"test-product-features.md\",\"excerpt\":\"\\n<p>这是一个用于测试ProductPageLayout.vue组件中产品特点中英文支持功能的测试页面。</p>\\n<h2>测试内容</h2>\\n<ol>\\n<li><strong>中英文特点产品</strong> - 包含3个中英文对象格式的特点</li>\\n<li><strong>混合格式测试</strong> - 包含新格式（中英文对象）和旧格式（字符串）的混合特点</li>\\n<li><strong>仅中文特点产品</strong> - 使用传统字符串格式的特点（向后兼容）</li>\\n<li><strong>无特点产品</strong> - 没有features字段，不应显示特点列表</li>\\n</ol>\"}")
export { comp, data }

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updatePageData) {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ data }) => {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  })
}
