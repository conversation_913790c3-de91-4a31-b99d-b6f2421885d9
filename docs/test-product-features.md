---
layout: ProductPageLayout
pageTitle: 产品特点中英文测试页面
productTitleCn: 产品特点中英文测试
productTitleEn: Product Features Bilingual Test
productList:
  - titleCn: 中英文特点产品
    titleEn: Bilingual Features Product
    image: https://img-public.hui1688.cn/shuangma/66040eece3f7a.png
    features:
      - cn: 高清TFT显示屏
        en: High-definition TFT display
      - cn: 蓝牙4.0无线连接
        en: Bluetooth 4.0 wireless connection
      - cn: IP67防水防尘
        en: IP67 waterproof and dustproof
  - primaryTitle: 混合格式测试
    titleCn: 新旧格式兼容测试
    titleEn: New and Old Format Compatibility Test
    image: https://img-public.hui1688.cn/shuangma/66040eb46ddcb.png
    features:
      - cn: 智能控制系统
        en: Smart control system
      - 旧格式特点（仅中文）
      - cn: NFC近场通信
        en: NFC near field communication
      - 超长续航设计
      - cn: 一键启动功能
        en: One-key start function
  - titleCn: 仅中文特点产品
    titleEn: Chinese Only Features Product
    image: https://img-public.hui1688.cn/shuangma/product-lcd.jpg
    features:
      - 传统字符串格式特点1
      - 传统字符串格式特点2
      - 传统字符串格式特点3
  - titleCn: 无特点产品
    titleEn: Product without Features
    image: https://img-public.hui1688.cn/shuangma/66040eb46ddcb.png
backUrl: /products/
showDetailContent: false
---

# 产品特点中英文测试页面

这是一个用于测试ProductPageLayout.vue组件中产品特点中英文支持功能的测试页面。

## 测试内容

1. **中英文特点产品** - 包含3个中英文对象格式的特点
2. **混合格式测试** - 包含新格式（中英文对象）和旧格式（字符串）的混合特点
3. **仅中文特点产品** - 使用传统字符串格式的特点（向后兼容）
4. **无特点产品** - 没有features字段，不应显示特点列表

## 预期效果

- 中英文对象格式的特点应该同时显示中文和英文
- 中文特点使用较大字体和深色
- 英文特点使用较小字体、浅色和斜体
- 字符串格式的特点应该正常显示（向后兼容）
- 混合格式应该能够正确处理
- 在不同屏幕尺寸下都应该有良好的显示效果

## 数据格式说明

### 新格式（推荐）
```yaml
features:
  - cn: 中文特点描述
    en: English feature description
```

### 旧格式（向后兼容）
```yaml
features:
  - 特点描述字符串
```
