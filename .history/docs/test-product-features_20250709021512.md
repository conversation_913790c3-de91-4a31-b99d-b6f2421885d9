---
layout: ProductPageLayout
pageTitle: 产品特点测试页面
productTitleCn: 产品特点测试
productTitleEn: Product Features Test
productList:
  - titleCn: 测试产品1
    titleEn: Test Product 1
    image: https://img-public.hui1688.cn/shuangma/66040eece3f7a.png
    features:
      - 特点1：高清显示
      - 特点2：蓝牙连接
      - 特点3：防水防尘
  - primaryTitle: 测试产品2
    titleCn: 带主标题的产品
    titleEn: Product with Primary Title
    image: https://img-public.hui1688.cn/shuangma/66040eb46ddcb.png
    features:
      - 智能控制系统
      - NFC功能
      - 超长续航
      - 一键启动
      - 语音控制
  - titleCn: 无特点产品
    titleEn: Product without Features
    image: https://img-public.hui1688.cn/shuangma/product-lcd.jpg
backUrl: /products/
showDetailContent: false
---

# 产品特点测试页面

这是一个用于测试ProductPageLayout.vue组件中每个产品项独立特点显示功能的测试页面。

## 测试内容

1. **测试产品1** - 包含3个特点
2. **测试产品2** - 包含5个特点，并有主标题
3. **无特点产品** - 没有features字段，不应显示特点列表

## 预期效果

- 每个产品的特点应该显示在产品标题下方
- 特点列表应该有合适的样式和图标
- 没有特点的产品不应显示特点区域
- 在不同屏幕尺寸下都应该有良好的显示效果
